org.gradle.jvmargs=-Xmx4G -XX:MaxMetaspaceSize=2G -XX:ReservedCodeCacheSize=512m -XX:+HeapDumpOnOutOfMemoryError
android.useAndroidX=true
android.enableJetifier=true

# Gradle optimization settings
org.gradle.daemon=true
org.gradle.parallel=true
org.gradle.configureondemand=false
org.gradle.caching=false

# Kotlin compiler settings
kotlin.daemon.jvmargs=-Xmx2G -XX:MaxMetaspaceSize=1G
kotlin.incremental=false
kotlin.caching.enabled=false

# Android build settings
android.enableR8.fullMode=false
android.useAndroidX=true
