# 📊 Platform Reports System - SafeDests

## نظام تقارير المنصة المتكامل

تم تطوير نظام تقارير شامل ومتقدم لمنصة SafeDests يتيح للمستخدمين إنشاء تقارير مخصصة ومفصلة مع إمكانيات تصدير متقدمة.

## 🎯 الميزات الرئيسية

### ✅ تقرير مهام العميل
- **فلترة متقدمة**: اختيار العملاء، الفترة الزمنية، حالة المهام، حالة الدفع، السائقين، والفرق
- **تخصيص الأعمدة**: اختيار من 13 عمود متاح مع حد أدنى 4 أعمدة
- **تصدير Excel**: تقرير مفصل مع تنسيق احترافي وإحصائيات
- **تصدير PDF**: تقرير مبسط للطباعة عبر المتصفح
- **معاينة مباشرة**: عرض البيانات قبل التصدير
- **حفظ التفضيلات**: حفظ اختيارات الأعمدة للاستخدام المستقبلي

## 📁 الملفات المُنشأة

### Backend Files
```
app/Http/Controllers/admin/PlatformReportsController.php
app/Services/ReportService.php
app/Exports/CustomerTasksExport.php
database/seeders/ReportsPermissionsSeeder.php
```

### Frontend Files
```
resources/views/admin/reports/
├── index.blade.php
├── customer-tasks.blade.php
└── pdf/
    └── customer-tasks-simple.blade.php

public/js/admin/reports/
└── customer-tasks.js

public/css/admin/
└── reports.css
```

### Configuration Files
```
routes/web.php (تم إضافة مسارات جديدة)
resources/menu/web/verticalMenu.json (تم إضافة رابط التقارير)
```

## 🔧 التقنيات المستخدمة

### Backend
- **Laravel 11.x** - إطار العمل الأساسي
- **Laravel Excel** - تصدير ملفات Excel المتقدمة
- **Spatie Permissions** - إدارة الصلاحيات
- **Laravel Sanctum** - حماية API

### Frontend
- **Bootstrap 5** - التصميم والتخطيط
- **DataTables** - عرض البيانات التفاعلي
- **Select2** - قوائم الاختيار المتقدمة
- **DateRangePicker** - اختيار الفترات الزمنية
- **SweetAlert2** - الرسائل التفاعلية
- **Moment.js** - معالجة التواريخ

## 🚀 كيفية الاستخدام

### 1. الوصول للتقارير
- انتقل إلى **لوحة التحكم** → **Platform Reports**
- اختر **تقرير مهام العميل**

### 2. تطبيق الفلاتر
- **اختيار العملاء**: اختر عميل واحد أو أكثر
- **الفترة الزمنية**: حدد من وإلى (مطلوب)
- **فلاتر إضافية**: حالة المهام، الدفع، السائقين، الفرق

### 3. تخصيص الأعمدة
- اختر الأعمدة المطلوبة (حد أدنى 4)
- الأعمدة المطلوبة: رقم المهمة + سعر المهمة
- يتم حفظ اختياراتك تلقائياً

### 4. المعاينة والتصدير
- اضغط **معاينة التقرير** لعرض البيانات
- اختر **تصدير Excel** للتقرير المفصل
- اختر **تصدير PDF** للتقرير المبسط

## 📊 الأعمدة المتاحة

| العمود | الوصف | مطلوب |
|--------|--------|--------|
| رقم المهمة | معرف المهمة الفريد | ✅ |
| سعر المهمة | المبلغ الإجمالي | ✅ |
| معلومات نقطة الاستلام | العنوان ومعلومات المسؤول | ❌ |
| معلومات نقطة التسليم | العنوان ومعلومات المسؤول | ❌ |
| اسم المركبة | نوع وحجم المركبة | ❌ |
| معلومات السائق | الاسم والهاتف والفريق | ❌ |
| حالة المهمة | الحالة الحالية للمهمة | ❌ |
| حالة الدفع | حالة الدفع | ❌ |
| طريقة الدفع | وسيلة الدفع المستخدمة | ❌ |
| منشئ المهمة | عميل أم إداري | ❌ |
| تاريخ الإنشاء | وقت إنشاء المهمة | ❌ |
| تاريخ الإكمال | وقت إكمال المهمة | ❌ |
| تاريخ الإغلاق | وقت إغلاق المهمة | ❌ |

## 🔒 الصلاحيات

تم إنشاء الصلاحيات التالية:
- `view_reports` - عرض صفحة التقارير
- `export_reports` - تصدير التقارير
- `customer_tasks_reports` - تقارير مهام العميل

## 📈 الإحصائيات والملخص

يتضمن كل تقرير:
- **إجمالي عدد المهام**
- **إجمالي المبلغ**
- **متوسط سعر المهمة**
- **توزيع حالات المهام**
- **توزيع حالات الدفع**

## 🎨 التصميم والواجهة

### الصفحة الرئيسية
- تصميم بطاقات تفاعلية
- ألوان متدرجة جذابة
- رموز واضحة ومعبرة
- معلومات شاملة عن كل تقرير

### صفحة تقرير مهام العميل
- نموذج فلترة متقدم
- واجهة تخصيص الأعمدة
- معاينة البيانات التفاعلية
- أزرار تصدير واضحة

### تقرير PDF
- تصميم مناسب للطباعة
- دعم كامل للغة العربية
- تخطيط أفقي للجداول
- معلومات شاملة في الرأس

## 🔧 الإعدادات التقنية

### متطلبات النظام
- PHP 8.2+
- Laravel 11.x
- MySQL 8.0+
- Node.js 18+ (للأصول الأمامية)

### الحزم المطلوبة
```bash
composer require maatwebsite/excel
```

### إعداد الصلاحيات
```bash
php artisan db:seed --class=ReportsPermissionsSeeder
```

## 🚨 الأمان والحماية

- **فحص الصلاحيات**: التحقق من صلاحية المستخدم
- **فلترة البيانات**: عرض البيانات حسب الصلاحيات
- **حماية CSRF**: حماية النماذج
- **تشفير البيانات**: حماية البيانات الحساسة
- **تسجيل العمليات**: تتبع إنشاء التقارير

## ⚡ الأداء والتحسين

- **التحميل التدريجي**: للتقارير الكبيرة
- **التخزين المؤقت**: للنتائج المتكررة
- **ضغط الملفات**: لملفات Excel الكبيرة
- **حدود النظام**: 10,000 سجل كحد أقصى

## 🔄 التطوير المستقبلي

### التقارير القادمة
- تقرير أداء السائقين
- التقارير المالية
- تقرير أداء الفرق
- منشئ التقارير المخصص
- تحليلات النظام

### الميزات المخطط لها
- جدولة التقارير التلقائية
- إرسال التقارير بالبريد الإلكتروني
- تقارير تفاعلية مع الرسوم البيانية
- تصدير إلى تنسيقات إضافية
- واجهة برمجة تطبيقات للتقارير

## 📞 الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
1. تحقق من سجلات النظام
2. تأكد من الصلاحيات
3. راجع إعدادات قاعدة البيانات
4. تواصل مع فريق التطوير

## 📝 ملاحظات مهمة

- يتم حفظ تفضيلات الأعمدة في المتصفح
- التقارير محدودة بـ 10,000 سجل لضمان الأداء
- تصدير PDF يستخدم خاصية الطباعة في المتصفح
- جميع التواريخ بالتوقيت المحلي
- دعم كامل للغة العربية في جميع التقارير

---

**تم التطوير بواسطة**: فريق SafeDests التقني  
**تاريخ الإنشاء**: 27 أغسطس 2025  
**الإصدار**: 1.0.0
