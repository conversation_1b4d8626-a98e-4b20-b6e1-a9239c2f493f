import 'package:flutter/foundation.dart';
import '../config/app_config.dart';
import '../models/api_response.dart';
import '../models/wallet.dart';
import 'api_service.dart';

class WalletService extends ChangeNotifier {
  static final WalletService _instance = WalletService._internal();
  factory WalletService() => _instance;
  WalletService._internal();

  final ApiService _apiService = ApiService();

  Wallet? _wallet;
  List<WalletTransaction> _transactions = [];
  EarningsStats? _earningsStats;
  bool _isLoading = false;
  String? _errorMessage;
  bool _hasError = false;

  // Getters
  Wallet? get wallet => _wallet;
  List<WalletTransaction> get transactions => _transactions;
  List<WalletTransaction> get recentTransactionsShort =>
      _transactions.take(5).toList();
  EarningsStats? get earningsStats => _earningsStats;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  bool get hasError => _hasError;
  bool get isEmpty => _transactions.isEmpty && !_isLoading;
  bool get hasData => _transactions.isNotEmpty;
  String get currencySymbol => 'ر.س';

  // Get wallet information
  Future<ApiResponse<WalletResponse>> getWallet() async {
    _setLoading(true);

    try {
      final response = await _apiService.get<WalletResponse>(
        AppConfig.walletEndpoint,
        fromJson: (data) => WalletResponse.fromJson(data['data'] ?? data),
      );

      if (response.isSuccess && response.data != null) {
        _wallet = response.data!.wallet;
        notifyListeners();
      }

      return response;
    } catch (e) {
      return ApiResponse<WalletResponse>(
        success: false,
        message: 'فشل في جلب بيانات المحفظة: $e',
      );
    } finally {
      _setLoading(false);
    }
  }

  // Get wallet transactions
  Future<ApiResponse<WalletTransactionsResponse>> getTransactions({
    int page = 1,
    int perPage = 20,
    String? type,
    String? from,
    String? to,
    bool refresh = false,
  }) async {
    if (page == 1 || refresh) {
      _clearError();
    }

    try {
      final queryParams = <String, String>{
        'page': page.toString(),
        'per_page': perPage.toString(),
        if (type != null) 'type': type,
        if (from != null) 'from': from,
        if (to != null) 'to': to,
      };

      debugPrint('Fetching transactions with params: $queryParams');

      final response = await _apiService.get<WalletTransactionsResponse>(
        AppConfig.transactionsEndpoint,
        queryParams: queryParams,
        fromJson: (data) =>
            WalletTransactionsResponse.fromJson(data['data'] ?? data),
      );

      if (response.isSuccess && response.data != null) {
        final newTransactions = response.data!.transactions;
        debugPrint('Received ${newTransactions.length} transactions from API');

        // Add some test transactions with attachments for demonstration
        final testTransactions = _createTestTransactionsWithAttachments();

        if (page == 1 || refresh) {
          _transactions = [...newTransactions, ...testTransactions];
        } else {
          _transactions.addAll(newTransactions);
        }

        _clearError();
        notifyListeners();
      } else {
        _setError(response.errorMessage ?? 'فشل في جلب المعاملات المالية');
        debugPrint('API Error: ${response.errorMessage}');
      }

      return response;
    } catch (e) {
      final errorMsg = 'فشل في جلب المعاملات المالية: $e';
      _setError(errorMsg);
      debugPrint('Exception in getTransactions: $e');

      return ApiResponse<WalletTransactionsResponse>(
        success: false,
        message: errorMsg,
      );
    }
  }

  // Get earnings statistics
  Future<ApiResponse<EarningsStatsResponse>> getEarningsStats({
    EarningsPeriod period = EarningsPeriod.month,
  }) async {
    try {
      final queryParams = <String, String>{
        'period': period.value,
      };

      final response = await _apiService.get<EarningsStatsResponse>(
        AppConfig.earningsStatsEndpoint,
        queryParams: queryParams,
        fromJson: (data) =>
            EarningsStatsResponse.fromJson(data['data'] ?? data),
      );

      if (response.isSuccess && response.data != null) {
        _earningsStats = response.data!.stats;
        notifyListeners();
      }

      return response;
    } catch (e) {
      return ApiResponse<EarningsStatsResponse>(
        success: false,
        message: 'فشل في جلب إحصائيات الأرباح: $e',
      );
    }
  }

  // Get transactions by type
  Future<ApiResponse<WalletTransactionsResponse>> getTransactionsByType(
    WalletTransactionType type, {
    int page = 1,
    int perPage = 20,
  }) async {
    return getTransactions(
      page: page,
      perPage: perPage,
      type: type.value,
    );
  }

  // Get credit transactions
  Future<ApiResponse<WalletTransactionsResponse>> getCreditTransactions({
    int page = 1,
    int perPage = 20,
  }) async {
    return getTransactionsByType(
      WalletTransactionType.credit,
      page: page,
      perPage: perPage,
    );
  }

  // Get debit transactions
  Future<ApiResponse<WalletTransactionsResponse>> getDebitTransactions({
    int page = 1,
    int perPage = 20,
  }) async {
    return getTransactionsByType(
      WalletTransactionType.debit,
      page: page,
      perPage: perPage,
    );
  }

  // Get commission transactions
  Future<ApiResponse<WalletTransactionsResponse>> getCommissionTransactions({
    int page = 1,
    int perPage = 20,
  }) async {
    return getTransactionsByType(
      WalletTransactionType.commission,
      page: page,
      perPage: perPage,
    );
  }

  // Get earnings for different periods
  Future<ApiResponse<EarningsStatsResponse>> getTodayEarnings() async {
    return getEarningsStats(period: EarningsPeriod.today);
  }

  Future<ApiResponse<EarningsStatsResponse>> getWeekEarnings() async {
    return getEarningsStats(period: EarningsPeriod.week);
  }

  Future<ApiResponse<EarningsStatsResponse>> getMonthEarnings() async {
    return getEarningsStats(period: EarningsPeriod.month);
  }

  Future<ApiResponse<EarningsStatsResponse>> getYearEarnings() async {
    return getEarningsStats(period: EarningsPeriod.year);
  }

  // Refresh wallet data
  Future<void> refreshWallet() async {
    await getWallet();
  }

  // Refresh transactions
  Future<void> refreshTransactions() async {
    await getTransactions(page: 1);
  }

  // Refresh earnings stats
  Future<void> refreshEarningsStats() async {
    await getEarningsStats();
  }

  // Refresh all wallet data
  Future<void> refreshAll() async {
    await Future.wait([
      refreshWallet(),
      refreshTransactions(),
      refreshEarningsStats(),
    ]);
  }

  // Clear wallet data
  void clearWalletData() {
    _wallet = null;
    _transactions.clear();
    _earningsStats = null;
    notifyListeners();
  }

  // Private methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String message) {
    _errorMessage = message;
    _hasError = true;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    _hasError = false;
    notifyListeners();
  }

  // Helper methods
  double get currentBalance => _wallet?.balance ?? 0.0;
  double get totalEarnings => _wallet?.totalEarnings ?? 0.0;
  double get pendingAmount => _wallet?.pendingAmount ?? 0.0;
  String get currency => _wallet?.currency ?? 'SAR';

  // Get transaction by ID
  WalletTransaction? getTransactionById(int id) {
    try {
      return _transactions.firstWhere((transaction) => transaction.id == id);
    } catch (e) {
      return null;
    }
  }

  // Get transactions by type from local list
  List<WalletTransaction> getLocalTransactionsByType(TransactionType type) {
    return _transactions
        .where((transaction) => transaction.type == type.value)
        .toList();
  }

  // Get credit transactions from local list
  List<WalletTransaction> get localCreditTransactions {
    return _transactions.where((transaction) => transaction.isCredit).toList();
  }

  // Get debit transactions from local list
  List<WalletTransaction> get localDebitTransactions {
    return _transactions.where((transaction) => transaction.isDebit).toList();
  }

  // Calculate total for transaction type
  double getTotalForTransactionType(TransactionType type) {
    return _transactions
        .where((transaction) => transaction.type == type.value)
        .fold(0.0, (sum, transaction) => sum + transaction.amount);
  }

  // Get recent transactions (last 10)
  List<WalletTransaction> get recentTransactions {
    final sortedTransactions = List<WalletTransaction>.from(_transactions);
    sortedTransactions.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    return sortedTransactions.take(10).toList();
  }

  // Check if has sufficient balance
  bool hasSufficientBalance(double amount) {
    return currentBalance >= amount;
  }

  // Format currency
  String formatCurrency(double amount) {
    return '${amount.toStringAsFixed(2)} $currency';
  }

  // Get commission display text
  String get commissionDisplayText {
    if (_wallet?.commission == null) return '';
    return _wallet!.commission.displayValue;
  }

  // Get transactions with attachments from API
  List<WalletTransaction> _createTestTransactionsWithAttachments() {
    // إرجاع قائمة فارغة - سيتم جلب البيانات من API
    return [];
  }
}
