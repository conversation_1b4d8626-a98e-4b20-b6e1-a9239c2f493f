<!doctype html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>خطة تطبيق صفحة تقارير المنصة</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        line-height: 1.6;
        color: #333;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
      }

      .header {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        padding: 40px;
        text-align: center;
        margin-bottom: 30px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      }

      .header h1 {
        color: #2c3e50;
        font-size: 2.5em;
        margin-bottom: 10px;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
      }

      .header .subtitle {
        color: #7f8c8d;
        font-size: 1.2em;
        margin-bottom: 20px;
      }

      .section {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 15px;
        margin-bottom: 25px;
        overflow: hidden;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease;
      }

      .section:hover {
        transform: translateY(-5px);
      }

      .section-header {
        background: linear-gradient(135deg, #e74c3c, #c0392b);
        color: white;
        padding: 20px 30px;
        font-size: 1.4em;
        font-weight: bold;
        display: flex;
        align-items: center;
        gap: 15px;
      }

      .section-content {
        padding: 30px;
      }

      .step-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
        margin: 20px 0;
      }

      .step-card {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border-radius: 12px;
        padding: 25px;
        border-left: 5px solid #3498db;
        transition: all 0.3s ease;
      }

      .step-card:hover {
        transform: translateX(-5px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
      }

      .step-card h4 {
        color: #2c3e50;
        margin-bottom: 15px;
        font-size: 1.2em;
      }

      .step-card ul {
        list-style: none;
        padding-right: 0;
      }

      .step-card li {
        padding: 8px 0;
        border-bottom: 1px solid #ecf0f1;
        position: relative;
        padding-right: 25px;
      }

      .step-card li:before {
        content: '✓';
        position: absolute;
        right: 0;
        color: #27ae60;
        font-weight: bold;
      }

      .code-block {
        background: #2c3e50;
        color: #ecf0f1;
        padding: 20px;
        border-radius: 8px;
        font-family: 'Courier New', monospace;
        font-size: 0.9em;
        overflow-x: auto;
        margin: 15px 0;
        border-left: 4px solid #3498db;
      }

      .file-structure {
        background: #34495e;
        color: #ecf0f1;
        padding: 20px;
        border-radius: 8px;
        font-family: 'Courier New', monospace;
        font-size: 0.85em;
        margin: 15px 0;
        border-left: 4px solid #e74c3c;
      }

      .info {
        background: linear-gradient(45deg, #3498db, #2980b9);
        color: white;
        padding: 15px;
        border-radius: 8px;
        margin: 15px 0;
        border-left: 4px solid #2980b9;
      }

      .warning {
        background: linear-gradient(45deg, #f39c12, #e67e22);
        color: white;
        padding: 15px;
        border-radius: 8px;
        margin: 15px 0;
        border-left: 4px solid #e67e22;
      }

      .success {
        background: linear-gradient(45deg, #27ae60, #229954);
        color: white;
        padding: 15px;
        border-radius: 8px;
        margin: 15px 0;
        border-left: 4px solid #229954;
      }

      .tech-item {
        background: linear-gradient(45deg, #9b59b6, #8e44ad);
        color: white;
        padding: 8px 15px;
        border-radius: 20px;
        font-weight: bold;
        font-size: 0.85em;
        display: inline-block;
        margin: 5px;
        box-shadow: 0 3px 10px rgba(155, 89, 182, 0.3);
      }

      .timeline {
        position: relative;
        padding: 20px 0;
      }

      .timeline-item {
        background: #fff;
        border-radius: 10px;
        padding: 20px;
        margin: 15px 0;
        border-left: 4px solid #3498db;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
      }

      .timeline-item h4 {
        color: #2c3e50;
        margin-bottom: 10px;
      }

      .phase-number {
        background: linear-gradient(45deg, #e74c3c, #c0392b);
        color: white;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        margin-left: 10px;
      }

      @media (max-width: 768px) {
        .container {
          padding: 10px;
        }

        .header {
          padding: 20px;
        }

        .header h1 {
          font-size: 2em;
        }

        .step-grid {
          grid-template-columns: 1fr;
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <!-- Header Section -->
      <div class="header">
        <h1>📊 خطة تطبيق صفحة تقارير المنصة</h1>
        <div class="subtitle">تقرير مفصل لتطبيق نظام التقارير المتقدم مع تقرير مهام العميل</div>
      </div>

      <!-- Overview -->
      <div class="section">
        <div class="section-header">🎯 نظرة عامة على المتطلبات</div>
        <div class="section-content">
          <div class="info">
            <strong>الهدف:</strong> إنشاء صفحة "تقارير المنصة" متخصصة في استخراج التقارير المخصصة مع البدء بتقرير مهام
            العميل الذي يدعم التصدير إلى Excel (مفصل) و PDF (مبسط).
          </div>

          <h3>📋 المتطلبات الأساسية:</h3>
          <div class="step-grid">
            <div class="step-card">
              <h4>🔍 نظام الفلترة</h4>
              <ul>
                <li>اختيار العميل</li>
                <li>تحديد الفترة الزمنية (من - إلى)</li>
                <li>حالة المهام (اختيار متعدد)</li>
                <li>حالة الدفع</li>
                <li>طريقة الدفع</li>
                <li>السائق/الفريق</li>
              </ul>
            </div>
            <div class="step-card">
              <h4>📊 التقرير المفصل (Excel)</h4>
              <ul>
                <li>أعمدة قابلة للتخصيص</li>
                <li>حد أدنى 4 أعمدة</li>
                <li>13 عمود متاح للاختيار</li>
                <li>تصدير Excel متقدم</li>
                <li>تنسيق احترافي</li>
              </ul>
            </div>
            <div class="step-card">
              <h4>📄 التقرير المبسط (PDF)</h4>
              <ul>
                <li>أعمدة ثابتة (7 أعمدة)</li>
                <li>تصدير PDF عبر المتصفح</li>
                <li>تصميم مبسط وواضح</li>
                <li>معلومات الشركة والعميل</li>
                <li>ملخص الفلاتر المطبقة</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      <!-- Technical Implementation Plan -->
      <div class="section">
        <div class="section-header">🛠️ خطة التطبيق التقني</div>
        <div class="section-content">
          <h3>📁 الملفات المطلوب إنشاؤها:</h3>
          <div class="file-structure">
            app/Http/Controllers/admin/PlatformReportsController.php app/Services/ReportService.php
            app/Exports/CustomerTasksExport.php resources/views/admin/reports/ ├── index.blade.php ├──
            customer-tasks.blade.php └── pdf/ └── customer-tasks-simple.blade.php resources/js/admin/reports/ ├──
            main.js ├── customer-tasks.js └── filters.js routes/web.php (إضافة مسارات جديدة)
          </div>

          <h3>🔧 التقنيات المستخدمة:</h3>
          <div style="text-align: center; margin: 20px 0">
            <span class="tech-item">Laravel Excel</span>
            <span class="tech-item">DomPDF</span>
            <span class="tech-item">DataTables</span>
            <span class="tech-item">Select2</span>
            <span class="tech-item">DateRangePicker</span>
            <span class="tech-item">SweetAlert2</span>
            <span class="tech-item">Bootstrap 5</span>
          </div>
        </div>
      </div>

      <!-- Implementation Phases -->
      <div class="section">
        <div class="section-header">📅 مراحل التطبيق</div>
        <div class="section-content">
          <div class="timeline">
            <div class="timeline-item">
              <h4><span class="phase-number">1</span>إعداد البنية الأساسية</h4>
              <ul>
                <li>إنشاء Controller للتقارير</li>
                <li>إعداد Service للتقارير</li>
                <li>إنشاء المسارات</li>
                <li>إعداد الصلاحيات</li>
              </ul>
            </div>

            <div class="timeline-item">
              <h4><span class="phase-number">2</span>تطوير واجهة المستخدم</h4>
              <ul>
                <li>صفحة التقارير الرئيسية</li>
                <li>نموذج فلترة تقرير مهام العميل</li>
                <li>واجهة تخصيص الأعمدة</li>
                <li>أزرار التصدير</li>
              </ul>
            </div>

            <div class="timeline-item">
              <h4><span class="phase-number">3</span>تطوير منطق التقارير</h4>
              <ul>
                <li>استعلامات قاعدة البيانات</li>
                <li>معالجة الفلاتر</li>
                <li>تجميع البيانات</li>
                <li>تحسين الأداء</li>
              </ul>
            </div>

            <div class="timeline-item">
              <h4><span class="phase-number">4</span>تطوير التصدير</h4>
              <ul>
                <li>تصدير Excel المفصل</li>
                <li>تصدير PDF المبسط</li>
                <li>تنسيق التقارير</li>
                <li>إضافة الشعارات والمعلومات</li>
              </ul>
            </div>

            <div class="timeline-item">
              <h4><span class="phase-number">5</span>الاختبار والتحسين</h4>
              <ul>
                <li>اختبار جميع الفلاتر</li>
                <li>اختبار التصدير</li>
                <li>تحسين الأداء</li>
                <li>إصلاح الأخطاء</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      <!-- Database Structure -->
      <div class="section">
        <div class="section-header">🗄️ هيكل قاعدة البيانات المطلوب</div>
        <div class="section-content">
          <h3>📊 الجداول المستخدمة في التقرير:</h3>
          <div class="code-block">
            الجداول الرئيسية: • tasks - المهام الأساسية • customers - بيانات العملاء • drivers - بيانات السائقين • teams
            - بيانات الفرق • task_points - نقاط الاستلام والتسليم • users - المستخدمين الإداريين • vehicle_sizes - أحجام
            المركبات • vehicle_types - أنواع المركبات • vehicles - المركبات الاستعلام الأساسي: SELECT tasks.*,
            customers.name as customer_name, drivers.name as driver_name, drivers.phone as driver_phone, teams.name as
            team_name, pickup.address as pickup_address, pickup.contact_name as pickup_contact, delivery.address as
            delivery_address, delivery.contact_name as delivery_contact, users.name as created_by_admin FROM tasks LEFT
            JOIN customers ON tasks.customer_id = customers.id LEFT JOIN drivers ON tasks.driver_id = drivers.id LEFT
            JOIN teams ON drivers.team_id = teams.id LEFT JOIN task_points pickup ON tasks.id = pickup.task_id AND
            pickup.type = 'pickup' LEFT JOIN task_points delivery ON tasks.id = delivery.task_id AND delivery.type =
            'delivery' LEFT JOIN users ON tasks.user_id = users.id WHERE conditions...
          </div>

          <h3>🔍 الفلاتر المتاحة:</h3>
          <div class="step-grid">
            <div class="step-card">
              <h4>📅 الفلاتر الزمنية</h4>
              <ul>
                <li>تاريخ الإنشاء (من - إلى)</li>
                <li>تاريخ الإكمال (من - إلى)</li>
                <li>تاريخ الإغلاق (من - إلى)</li>
                <li>فلاتر سريعة (اليوم، الأسبوع، الشهر)</li>
              </ul>
            </div>
            <div class="step-card">
              <h4>📋 فلاتر الحالة</h4>
              <ul>
                <li>حالة المهمة (متعدد الاختيار)</li>
                <li>حالة الدفع (waiting, completed, pending)</li>
                <li>طريقة الدفع (cash, credit, banking, wallet)</li>
                <li>حالة الإغلاق (مفتوحة/مغلقة)</li>
              </ul>
            </div>
            <div class="step-card">
              <h4>👥 فلاتر الأشخاص</h4>
              <ul>
                <li>العميل (اختيار واحد أو متعدد)</li>
                <li>السائق (اختيار متعدد)</li>
                <li>الفريق (اختيار متعدد)</li>
                <li>منشئ المهمة (عميل/إداري)</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      <!-- Detailed Columns Configuration -->
      <div class="section">
        <div class="section-header">📊 تكوين أعمدة التقارير</div>
        <div class="section-content">
          <h3>📋 التقرير المفصل - الأعمدة المتاحة (13 عمود):</h3>
          <div class="code-block">
            الأعمدة المتاحة للتخصيص: 1. رقم المهمة (task_id) - مطلوب دائماً 2. سعر المهمة (total_price) - مطلوب دائماً
            3. عنوان نقطة الاستلام + معلومات المسؤول 4. عنوان نقطة التسليم + معلومات المسؤول 5. اسم المركبة المستخدمة 6.
            معلومات السائق (اسم + هاتف + فريق) 7. حالة المهمة (status) 8. حالة الدفع (payment_status) 9. طريقة الدفع
            (payment_method) 10. منشئ المهمة (customer/admin) 11. تاريخ الإنشاء (created_at) 12. تاريخ الإكمال
            (completed_at) 13. تاريخ الإغلاق (closed_at) الحد الأدنى: 4 أعمدة (رقم المهمة + السعر + عمودين اختياريين)
          </div>

          <h3>📄 التقرير المبسط - الأعمدة الثابتة (7 أعمدة):</h3>
          <div class="code-block">
            الأعمدة الثابتة (غير قابلة للتخصيص): 1. رقم المهمة 2. سعر المهمة 3. عنوان الاستلام → عنوان التسليم 4. السائق
            + رقمه + الفريق 5. حالة المهمة 6. تاريخ الإنشاء 7. تاريخ الإغلاق
          </div>

          <div class="warning">
            <strong>ملاحظة مهمة:</strong> سيتم حفظ تفضيلات المستخدم لتخصيص الأعمدة في Local Storage للاستخدام المستقبلي.
          </div>
        </div>
      </div>

      <!-- Export Features -->
      <div class="section">
        <div class="section-header">📤 ميزات التصدير المتقدمة</div>
        <div class="section-content">
          <h3>📊 تصدير Excel المفصل:</h3>
          <div class="step-grid">
            <div class="step-card">
              <h4>🎨 التنسيق والتصميم</h4>
              <ul>
                <li>شعار الشركة في الأعلى</li>
                <li>معلومات الشركة والعميل</li>
                <li>تاريخ التقرير والفلاتر</li>
                <li>تنسيق الخلايا والألوان</li>
                <li>عرض الأعمدة التلقائي</li>
              </ul>
            </div>
            <div class="step-card">
              <h4>📈 الميزات المتقدمة</h4>
              <ul>
                <li>إجمالي المبالغ في النهاية</li>
                <li>عدد المهام حسب الحالة</li>
                <li>إحصائيات سريعة</li>
                <li>تجميد الصف الأول</li>
                <li>فلترة تلقائية للأعمدة</li>
              </ul>
            </div>
          </div>

          <h3>📄 تصدير PDF المبسط:</h3>
          <div class="step-grid">
            <div class="step-card">
              <h4>🖨️ إعدادات الطباعة</h4>
              <ul>
                <li>حجم الورق A4</li>
                <li>اتجاه أفقي للجدول</li>
                <li>هوامش مناسبة</li>
                <li>خط واضح ومقروء</li>
                <li>فواصل الصفحات التلقائية</li>
              </ul>
            </div>
            <div class="step-card">
              <h4>📋 المحتوى</h4>
              <ul>
                <li>رأس الصفحة مع الشعار</li>
                <li>معلومات التقرير</li>
                <li>جدول البيانات المبسط</li>
                <li>ملخص في النهاية</li>
                <li>رقم الصفحة</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      <!-- Security and Performance -->
      <div class="section">
        <div class="section-header">🔒 الأمان والأداء</div>
        <div class="section-content">
          <h3>🛡️ إجراءات الأمان:</h3>
          <div class="info">
            <ul>
              <li><strong>التحقق من الصلاحيات:</strong> التأكد من صلاحية المستخدم لعرض بيانات العملاء</li>
              <li><strong>فلترة البيانات:</strong> عرض البيانات حسب صلاحيات المستخدم فقط</li>
              <li><strong>تشفير المعاملات:</strong> حماية عمليات التصدير</li>
              <li><strong>تسجيل العمليات:</strong> تسجيل عمليات إنشاء التقارير للمراجعة</li>
            </ul>
          </div>

          <h3>⚡ تحسين الأداء:</h3>
          <div class="success">
            <ul>
              <li><strong>التحميل التدريجي:</strong> تحميل البيانات على دفعات للتقارير الكبيرة</li>
              <li><strong>التخزين المؤقت:</strong> حفظ النتائج المتكررة</li>
              <li><strong>الفهرسة:</strong> التأكد من وجود فهارس مناسبة</li>
              <li><strong>ضغط الملفات:</strong> ضغط ملفات Excel الكبيرة</li>
            </ul>
          </div>

          <h3>📊 حدود النظام:</h3>
          <div class="warning">
            <ul>
              <li><strong>حد أقصى للسجلات:</strong> 10,000 سجل في التقرير الواحد</li>
              <li><strong>مهلة زمنية:</strong> 5 دقائق كحد أقصى لإنشاء التقرير</li>
              <li><strong>حجم الملف:</strong> 50 ميجابايت كحد أقصى للتصدير</li>
              <li><strong>التقارير المتزامنة:</strong> تقرير واحد لكل مستخدم في نفس الوقت</li>
            </ul>
          </div>
        </div>
      </div>

      <!-- Timeline and Approval -->
      <div class="section">
        <div class="section-header">⏱️ الجدول الزمني والموافقة</div>
        <div class="section-content">
          <h3>📅 الجدول الزمني المقترح:</h3>
          <div class="timeline">
            <div class="timeline-item">
              <h4>🔧 المرحلة الأولى (يوم 1-2)</h4>
              <p>إعداد البنية الأساسية والـ Controllers والـ Services</p>
            </div>
            <div class="timeline-item">
              <h4>🎨 المرحلة الثانية (يوم 3-4)</h4>
              <p>تطوير واجهات المستخدم ونماذج الفلترة</p>
            </div>
            <div class="timeline-item">
              <h4>💾 المرحلة الثالثة (يوم 5-6)</h4>
              <p>تطوير منطق التقارير واستعلامات قاعدة البيانات</p>
            </div>
            <div class="timeline-item">
              <h4>📤 المرحلة الرابعة (يوم 7-8)</h4>
              <p>تطوير ميزات التصدير Excel و PDF</p>
            </div>
            <div class="timeline-item">
              <h4>🧪 المرحلة الخامسة (يوم 9-10)</h4>
              <p>الاختبار الشامل والتحسينات النهائية</p>
            </div>
          </div>

          <div class="success">
            <h3>✅ جاهز للتطبيق</h3>
            <p>
              هذه الخطة شاملة ومفصلة لتطبيق نظام تقارير المنصة مع تقرير مهام العميل. جميع المتطلبات المذكورة سيتم
              تطبيقها بدقة مع مراعاة أفضل الممارسات في الأمان والأداء.
            </p>

            <p><strong>في انتظار موافقتك لبدء التطبيق! 🚀</strong></p>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>
