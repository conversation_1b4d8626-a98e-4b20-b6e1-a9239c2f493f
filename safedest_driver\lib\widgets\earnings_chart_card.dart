import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/wallet_service.dart';

class EarningsChartCard extends StatelessWidget {
  const EarningsChartCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<WalletService>(
      builder: (context, walletService, child) {
        return Card(
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.bar_chart,
                      color: Theme.of(context).colorScheme.primary,
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    Text(
                      'إحصائيات الأرباح',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                  ],
                ),

                const SizedBox(height: 20),

                // Period Selector
                _buildPeriodSelector(context),

                const SizedBox(height: 20),

                // Chart Placeholder (will be replaced with actual chart)
                _buildChartPlaceholder(context),

                const SizedBox(height: 20),

                // Quick Stats
                _buildQuickStats(context, walletService),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildPeriodSelector(BuildContext context) {
    return Row(
      children: [
        _buildPeriodChip(context, 'اليوم', true),
        const SizedBox(width: 8),
        _buildPeriodChip(context, 'الأسبوع', false),
        const SizedBox(width: 8),
        _buildPeriodChip(context, 'الشهر', false),
        const SizedBox(width: 8),
        _buildPeriodChip(context, 'السنة', false),
      ],
    );
  }

  Widget _buildPeriodChip(BuildContext context, String label, bool isSelected) {
    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        // TODO: Implement period selection
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('تم اختيار فترة: $label')),
        );
      },
      selectedColor: Theme.of(context).colorScheme.primary.withOpacity(0.2),
      checkmarkColor: Theme.of(context).colorScheme.primary,
    );
  }

  Widget _buildChartPlaceholder(BuildContext context) {
    return Container(
      height: 200,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.show_chart,
              size: 48,
              color: Theme.of(context).colorScheme.primary.withOpacity(0.5),
            ),
            const SizedBox(height: 12),
            Text(
              'الرسم البياني للأرباح',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: Theme.of(context)
                        .colorScheme
                        .onSurface
                        .withOpacity(0.6),
                  ),
            ),
            const SizedBox(height: 8),
            Text(
              'قريباً',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context)
                        .colorScheme
                        .onSurface
                        .withOpacity(0.5),
                  ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickStats(BuildContext context, WalletService walletService) {
    // Calculate stats from actual transactions
    final transactions = walletService.transactions;
    final creditTransactions = transactions.where((t) => t.isCredit).toList();

    if (creditTransactions.isEmpty) {
      return _buildEmptyStats(context);
    }

    // Calculate statistics
    final totalEarnings = creditTransactions.fold<double>(
      0.0,
      (sum, transaction) => sum + transaction.amount,
    );

    final averageEarning = totalEarnings / creditTransactions.length;
    final dailyAverage = totalEarnings / 30; // Assuming last 30 days

    // Calculate highest earning
    final highestEarning = creditTransactions.isNotEmpty
        ? creditTransactions
            .map((t) => t.amount)
            .reduce((a, b) => a > b ? a : b)
        : 0.0;

    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            context,
            'أعلى ربح',
            '${highestEarning.toStringAsFixed(2)} ر.س',
            Icons.trending_up,
            Colors.green,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            context,
            'متوسط يومي',
            '${dailyAverage.toStringAsFixed(2)} ر.س',
            Icons.today,
            Colors.blue,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            context,
            'إجمالي الأرباح',
            '${totalEarnings.toStringAsFixed(2)} ر.س',
            Icons.account_balance_wallet,
            Colors.orange,
          ),
        ),
      ],
    );
  }

  Widget _buildEmptyStats(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.info_outline,
            color: Colors.grey[600],
            size: 20,
          ),
          const SizedBox(width: 8),
          Text(
            'لا توجد معاملات مالية بعد',
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(
    BuildContext context,
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: color.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: color,
            size: 20,
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color:
                      Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
