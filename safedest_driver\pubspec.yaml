name: safedest_driver
description: "Safedest Driver"
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'  # تم تحديثها لتكون متوافقة

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # UI & Icons
  cupertino_icons: ^1.0.8
  flutter_svg: ^2.0.9
  google_fonts: ^6.1.0

  # HTTP & API
  http: ^1.1.2
  dio: ^5.4.0

  # State Management
  provider: ^6.1.1

  # Local Storage
  shared_preferences: ^2.2.2
  flutter_secure_storage: ^9.0.0

  # Location & Maps
  geolocator: ^10.1.0
  permission_handler: ^11.1.0
  google_maps_flutter: ^2.5.0

  # Push Notifications - Now enabled with Firebase
  firebase_core: ^2.30.0        # تم التحديث
  firebase_messaging: ^14.7.1   # تم التحديث
  flutter_local_notifications: ^19.4.1

  # Utils
  intl: ^0.20.2                 # تم التحديث (هذا هو الإصلاح الرئيسي)
  url_launcher: ^6.2.2
  image_picker: ^1.0.4
  file_picker: ^10.3.2
  cached_network_image: ^3.3.0
  open_file: ^3.3.2

  # UI Components
  flutter_spinkit: ^5.2.0
  shimmer: ^3.0.0
  pull_to_refresh: ^2.0.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0

flutter:
  uses-material-design: true