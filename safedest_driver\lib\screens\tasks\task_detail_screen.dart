import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../models/task.dart';
import '../../services/task_service.dart';
import '../../widgets/task_status_stepper.dart';
import '../../widgets/task_history_sheet.dart';

class TaskDetailScreen extends StatefulWidget {
  final Task task;

  const TaskDetailScreen({
    super.key,
    required this.task,
  });

  @override
  State<TaskDetailScreen> createState() => _TaskDetailScreenState();
}

class _TaskDetailScreenState extends State<TaskDetailScreen> {
  Task? _currentTask;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _currentTask = widget.task;
    _loadTaskDetails();
  }

  Future<void> _loadTaskDetails() async {
    final taskService = Provider.of<TaskService>(context, listen: false);

    try {
      final response = await taskService.getTaskDetails(widget.task.id);

      if (mounted) {
        setState(() {
          if (response.isSuccess && response.data != null) {
            _currentTask = response.data!;
          }
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في جلب تفاصيل المهمة: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        backgroundColor: Colors.grey[50],
        body: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (_currentTask == null) {
      return Scaffold(
        backgroundColor: Colors.grey[50],
        appBar: AppBar(
          title: const Text('خطأ'),
        ),
        body: const Center(
          child: Text('لم يتم العثور على المهمة'),
        ),
      );
    }

    final task = _currentTask!;

    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: CustomScrollView(
        slivers: [
          // Modern App Bar
          SliverAppBar(
            expandedHeight: 250,
            floating: false,
            pinned: true,
            backgroundColor: Theme.of(context).primaryColor,
            actions: [
              IconButton(
                onPressed: () => _showTaskHistory(),
                icon: const Icon(Icons.history, color: Colors.white),
                tooltip: 'سجل المهمة',
              ),
            ],
            flexibleSpace: FlexibleSpaceBar(
              title: Text(
                'مهمة #${task.id}',
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
              background: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Theme.of(context).primaryColor,
                      Theme.of(context).primaryColor.withValues(alpha: 0.8),
                      Colors.blue[600]!,
                    ],
                  ),
                ),
                child: Stack(
                  children: [
                    // Background Pattern
                    Positioned(
                      top: -50,
                      right: -50,
                      child: Container(
                        width: 200,
                        height: 200,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: Colors.white.withValues(alpha: 0.1),
                        ),
                      ),
                    ),
                    Positioned(
                      bottom: -30,
                      left: -30,
                      child: Container(
                        width: 150,
                        height: 150,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: Colors.white.withValues(alpha: 0.05),
                        ),
                      ),
                    ),
                    // Content
                    Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const SizedBox(height: 60),
                          // Status Badge
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 20,
                              vertical: 10,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(25),
                              border: Border.all(
                                color: Colors.white.withValues(alpha: 0.3),
                                width: 1,
                              ),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  _getStatusIcon(task.status),
                                  color: Colors.white,
                                  size: 20,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  TaskStatusExtension.fromString(task.status)
                                      .displayName,
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(height: 16),
                          // Earnings
                          Column(
                            children: [
                              const Text(
                                'مستحقاتك',
                                style: TextStyle(
                                  color: Colors.white70,
                                  fontSize: 14,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                '${task.driverEarnings.toStringAsFixed(2)} ر.س',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 28,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),

          // Content
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  // Quick Actions Card
                  _buildQuickActionsCard(),

                  const SizedBox(height: 16),

                  // Task Status Stepper
                  TaskStatusStepper(task: task),

                  const SizedBox(height: 16),

                  // Action Buttons
                  _buildActionButtons(),
                  const SizedBox(height: 16),

                  // Pickup Point Card
                  if (task.pickupPoint != null) _buildPickupPointCard(),

                  const SizedBox(height: 16),

                  // Delivery Point Card
                  if (task.deliveryPoint != null) _buildDeliveryPointCard(),

                  const SizedBox(height: 16),

                  // Items Card
                  if (task.items != null && task.items!.isNotEmpty)
                    _buildItemsCard(),

                  const SizedBox(height: 32),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActionsCard() {
    final task = _currentTask!;
    if (task.pickupPoint == null && task.deliveryPoint == null) {
      return const SizedBox.shrink();
    }

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.blue[100],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.navigation,
                    color: Colors.blue[700],
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  'التنقل السريع',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                if (task.pickupPoint != null)
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () => _openGoogleMaps(
                        task.pickupPoint!.latitude,
                        task.pickupPoint!.longitude,
                        'نقطة الاستلام',
                      ),
                      icon: const Icon(Icons.location_on, size: 20),
                      label: const Text('نقطة الاستلام'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    ),
                  ),
                if (task.pickupPoint != null && task.deliveryPoint != null)
                  const SizedBox(width: 12),
                if (task.deliveryPoint != null)
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () => _openGoogleMaps(
                        task.deliveryPoint!.latitude,
                        task.deliveryPoint!.longitude,
                        'نقطة التسليم',
                      ),
                      icon: const Icon(Icons.flag, size: 20),
                      label: const Text('نقطة التسليم'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPickupPointCard() {
    final task = _currentTask!;
    final point = task.pickupPoint!;
    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: Colors.green[100],
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.location_on,
                    color: Colors.green[700],
                    size: 28,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'نقطة الاستلام',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: Colors.green[700],
                            ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        point.address,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Colors.grey[600],
                            ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            const Divider(),
            const SizedBox(height: 16),
            if (point.contactName != null) ...[
              _buildContactRow(Icons.person, 'اسم المسؤول', point.contactName!),
              const SizedBox(height: 12),
            ],
            if (point.contactPhone != null) ...[
              _buildContactRow(Icons.phone, 'رقم الهاتف', point.contactPhone!),
              const SizedBox(height: 12),
            ],
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () => _openGoogleMaps(
                  point.latitude,
                  point.longitude,
                  'نقطة الاستلام',
                ),
                icon: const Icon(Icons.navigation, size: 20),
                label: const Text('فتح في خرائط جوجل'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 14),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDeliveryPointCard() {
    final task = _currentTask!;
    final point = task.deliveryPoint!;
    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: Colors.red[100],
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.flag,
                    color: Colors.red[700],
                    size: 28,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'نقطة التسليم',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: Colors.red[700],
                            ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        point.address,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Colors.grey[600],
                            ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            const Divider(),
            const SizedBox(height: 16),
            if (point.contactName != null) ...[
              _buildContactRow(Icons.person, 'اسم المسؤول', point.contactName!),
              const SizedBox(height: 12),
            ],
            if (point.contactPhone != null) ...[
              _buildContactRow(Icons.phone, 'رقم الهاتف', point.contactPhone!),
              const SizedBox(height: 12),
            ],
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () => _openGoogleMaps(
                  point.latitude,
                  point.longitude,
                  'نقطة التسليم',
                ),
                icon: const Icon(Icons.navigation, size: 20),
                label: const Text('فتح في خرائط جوجل'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 14),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContactRow(IconData icon, String label, String value) {
    return Row(
      children: [
        Icon(icon, size: 20, color: Colors.grey[600]),
        const SizedBox(width: 12),
        Text(
          '$label: ',
          style: TextStyle(
            fontWeight: FontWeight.w500,
            color: Colors.grey[700],
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }

  IconData _getStatusIcon(String status) {
    switch (status) {
      case 'assign':
        return Icons.assignment;
      case 'started':
        return Icons.play_arrow;
      case 'in pickup point':
        return Icons.location_on;
      case 'loading':
        return Icons.upload;
      case 'in the way':
        return Icons.local_shipping;
      case 'in delivery point':
        return Icons.flag;
      case 'unloading':
        return Icons.download;
      case 'completed':
        return Icons.done_all;
      case 'cancelled':
        return Icons.cancel;
      default:
        return Icons.pending;
    }
  }

  void _showTaskHistory() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => TaskHistorySheet(task: _currentTask!),
    );
  }

  Widget _buildItemsCard() {
    final task = _currentTask!;
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'العناصر',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 16),
            ...task.items!.map((item) => Padding(
                  padding: const EdgeInsets.only(bottom: 8),
                  child: Row(
                    children: [
                      Expanded(
                        child: Text('عنصر غير محدد'),
                      ),
                      Text('الكمية: ${item.quantity}'),
                    ],
                  ),
                )),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Consumer<TaskService>(
      builder: (context, taskService, child) {
        return _getActionButtonForStatus(_currentTask!.status, taskService);
      },
    );
  }

  Widget _getActionButtonForStatus(String status, TaskService taskService) {
    switch (status) {
      case 'pending':
        // أزرار القبول والرفض تظهر فقط في المهام المتاحة
        // هنا نعرض فقط معلومات الحالة
        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.orange[50],
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.orange[200]!),
          ),
          child: Row(
            children: [
              Icon(Icons.pending, color: Colors.orange[700]),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'في انتظار الموافقة',
                  style: TextStyle(
                    color: Colors.orange[700],
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        );
      case 'assign':
        return ElevatedButton.icon(
          onPressed: () => _updateTaskStatus(taskService, 'started'),
          icon: const Icon(Icons.play_arrow),
          label: const Text('بدء المهمة'),
          style: ElevatedButton.styleFrom(
            minimumSize: const Size(double.infinity, 48),
          ),
        );
      case 'started':
        return ElevatedButton.icon(
          onPressed: () => _updateTaskStatus(taskService, 'in pickup point'),
          icon: const Icon(Icons.location_on),
          label: const Text('وصلت لنقطة الاستلام'),
          style: ElevatedButton.styleFrom(
            minimumSize: const Size(double.infinity, 48),
          ),
        );
      case 'in pickup point':
        return ElevatedButton.icon(
          onPressed: () => _updateTaskStatus(taskService, 'loading'),
          icon: const Icon(Icons.upload),
          label: const Text('بدء التحميل'),
          style: ElevatedButton.styleFrom(
            minimumSize: const Size(double.infinity, 48),
          ),
        );
      case 'loading':
        return ElevatedButton.icon(
          onPressed: () => _updateTaskStatus(taskService, 'in the way'),
          icon: const Icon(Icons.local_shipping),
          label: const Text('في الطريق'),
          style: ElevatedButton.styleFrom(
            minimumSize: const Size(double.infinity, 48),
          ),
        );
      case 'in the way':
        return ElevatedButton.icon(
          onPressed: () => _updateTaskStatus(taskService, 'in delivery point'),
          icon: const Icon(Icons.location_on),
          label: const Text('وصلت لنقطة التسليم'),
          style: ElevatedButton.styleFrom(
            minimumSize: const Size(double.infinity, 48),
          ),
        );
      case 'in delivery point':
        return ElevatedButton.icon(
          onPressed: () => _updateTaskStatus(taskService, 'unloading'),
          icon: const Icon(Icons.download),
          label: const Text('بدء التفريغ'),
          style: ElevatedButton.styleFrom(
            minimumSize: const Size(double.infinity, 48),
          ),
        );
      case 'unloading':
        return ElevatedButton.icon(
          onPressed: () => _updateTaskStatus(taskService, 'completed'),
          icon: const Icon(Icons.done),
          label: const Text('إكمال المهمة'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.green,
            minimumSize: const Size(double.infinity, 48),
          ),
        );
      default:
        return const SizedBox.shrink();
    }
  }

  Future<void> _openGoogleMaps(
      double latitude, double longitude, String label) async {
    final url =
        'https://www.google.com/maps/search/?api=1&query=$latitude,$longitude';

    try {
      final uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('لا يمكن فتح خرائط جوجل')),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('حدث خطأ في فتح الخريطة')),
        );
      }
    }
  }

  Future<void> _updateTaskStatus(
      TaskService taskService, String newStatus) async {
    // Show loading dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(
        child: Card(
          child: Padding(
            padding: EdgeInsets.all(20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text('جاري تحديث حالة المهمة...'),
              ],
            ),
          ),
        ),
      ),
    );

    try {
      final response =
          await taskService.updateTaskStatus(_currentTask!.id, newStatus);

      if (mounted) {
        Navigator.of(context).pop(); // Close loading dialog

        if (response.isSuccess) {
          // Show success animation
          _showStatusUpdateSuccess(newStatus);

          // Refresh task data
          await taskService.getTasks();

          // Update UI
          setState(() {
            // The task will be updated through the service
          });
        } else {
          _showStatusUpdateError(response.errorMessage);
        }
      }
    } catch (e) {
      if (mounted) {
        Navigator.of(context).pop(); // Close loading dialog
        _showStatusUpdateError('حدث خطأ غير متوقع: $e');
      }
    }
  }

  void _showStatusUpdateSuccess(String newStatus) {
    final statusName = TaskStatusExtension.fromString(newStatus).displayName;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.green[100],
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.check,
                color: Colors.green[700],
                size: 48,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'تم التحديث بنجاح',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 8),
            Text(
              'تم تحديث حالة المهمة إلى: $statusName',
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.grey[600]),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('حسناً'),
          ),
        ],
      ),
    );
  }

  void _showStatusUpdateError(String errorMessage) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.red[100],
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.error,
                color: Colors.red[700],
                size: 48,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'فشل في التحديث',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 8),
            Text(
              errorMessage,
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.grey[600]),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('حسناً'),
          ),
        ],
      ),
    );
  }
}
